# Test console handler with string formatter
Application.put_env(:drops, :logger, %{
  handler: :console,
  formatter: :string,
  level: :debug,
  metadata: [:operation, :step]
})

# Remove existing handler and add new one with string formatter
:logger.remove_handler(:drops_debug_handler)

# Remove the default handler to only see our string output
:logger.remove_handler(:default)

result = Drops.Logger.add_handler()
IO.puts("Handler add result: #{inspect(result)}")

# Load the setup for examples
Code.require_file("examples/setup.exs")

_pid = ExampleSetup.setup_database([Test.Ecto.TestSchemas.UserSchema])

defmodule TestConsoleUser do
  use Drops.Operations.Command, repo: Drops.TestRepo, debug: true

  schema(Test.Ecto.TestSchemas.UserSchema)

  steps do
    @impl true
    def execute(%{changeset: changeset}) do
      insert(changeset)
    end
  end

  def validate_changeset(%{changeset: changeset}) do
    changeset
    |> validate_required([:name, :email])
  end
end

IO.puts("Testing console handler with string formatter (string only)...")

invalid_params = %{
  email: "<EMAIL>",
  name: ""
}

case TestConsoleUser.call(%{params: invalid_params}) do
  {:ok, user} ->
    IO.puts("✓ User saved successfully:")
    IO.inspect(user, pretty: true)

  {:error, reason} ->
    IO.puts("✗ Failed to save user:")
    IO.inspect(reason, pretty: true)
end
