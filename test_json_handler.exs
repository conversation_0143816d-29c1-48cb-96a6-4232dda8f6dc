# Test console handler with JSON formatter
Application.put_env(:drops, :logger, %{
  handler: :console,
  formatter: :json,
  level: :debug,
  metadata: [:operation, :step]
})

# Remove existing handler and add new one with JSON formatter
:logger.remove_handler(:drops_debug_handler)

# Remove the default handler to only see our JSON output
:logger.remove_handler(:default)

result = Drops.Logger.add_handler()
IO.puts("Handler add result: #{inspect(result)}")

# Check if handler was added
handler_config = :logger.get_handler_config(:drops_debug_handler)
IO.puts("Handler config: #{inspect(handler_config)}")

# Load the setup for examples
Code.require_file("examples/setup.exs")

_pid = ExampleSetup.setup_database([Test.Ecto.TestSchemas.UserSchema])

defmodule TestJsonUser do
  use Drops.Operations.Command, repo: Drops.TestRepo, debug: true

  schema(Test.Ecto.TestSchemas.UserSchema)

  steps do
    @impl true
    def execute(%{changeset: changeset}) do
      insert(changeset)
    end
  end

  def validate_changeset(%{changeset: changeset}) do
    changeset
    |> validate_required([:name, :email])
  end
end

IO.puts("Testing console handler with JSON formatter (JSON only)...")

valid_params = %{
  email: "<EMAIL>",
  name: "Test User"
}

case TestJsonUser.call(%{params: valid_params}) do
  {:ok, user} ->
    IO.puts("✓ User saved successfully:")
    IO.inspect(user, pretty: true)

  {:error, reason} ->
    IO.puts("✗ Failed to save user:")
    IO.inspect(reason, pretty: true)
end
