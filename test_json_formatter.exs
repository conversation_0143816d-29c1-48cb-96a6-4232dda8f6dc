# Quick test to verify formatters work with built-in handlers
alias Drops.Logger.Formatter

# Test the JSON formatter
log_event = %{
  level: :debug,
  msg: "Test message",
  meta: %{operation: "TestOp", step: :test_step}
}

json_output = Formatter.json(log_event, %{})
IO.puts("JSON Format:")
IO.puts(json_output)

# Test the string formatter
string_output = Formatter.string(log_event, %{})
IO.puts("\nString Format:")
IO.puts(string_output)

# Test with non-operation log (should be filtered out)
non_op_log = %{
  level: :debug,
  msg: "Regular log message",
  meta: %{some_key: "some_value"}
}

filtered_json = Formatter.json(non_op_log, %{})
filtered_string = Formatter.string(non_op_log, %{})

IO.puts("\nFiltered outputs (should be empty):")
IO.puts("JSON: '#{filtered_json}'")
IO.puts("String: '#{filtered_string}'")
