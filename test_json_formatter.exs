# Quick test to verify JSON formatter works
# Let's just test the formatter function directly
alias Drops.Logger.DebugHandler

# Test the JSON formatter
log_event = %{
  level: :debug,
  msg: "Test message",
  meta: %{operation: "TestOp", step: :test_step}
}

config = %{
  formatter: :json,
  metadata: [:operation, :step]
}

json_output = DebugHandler.format(log_event, config)
IO.puts("JSON Format:")
IO.puts(json_output)

# Test the string formatter
string_config = %{
  formatter: :string,
  metadata: [:operation, :step]
}

string_output = DebugHandler.format(log_event, string_config)
IO.puts("\nString Format:")
IO.puts(string_output)


