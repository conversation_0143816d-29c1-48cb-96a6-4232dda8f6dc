defmodule Drops.Logger.FormatterTest do
  use ExUnit.Case, async: true

  alias Drops.Logger.Formatter

  describe "format/2 with string formatter" do
    test "formats operation logs with string formatter" do
      log_event = %{
        level: :debug,
        msg: {:string, "Starting step execute"},
        meta: %{operation: "TestOp", step: :execute},
        time: System.os_time(:microsecond)
      }

      result = Formatter.format(log_event, :string)

      assert result =~ "[debug] Starting step execute"
      assert result =~ "operation=\"TestOp\""
      assert result =~ "step=:execute"
    end

    test "formats operation logs with binary message" do
      log_event = %{
        level: :info,
        msg: "Test message",
        meta: %{operation: "TestOp"},
        time: System.os_time(:microsecond)
      }

      result = Formatter.format(log_event, :string)

      assert result =~ "[info] Test message"
      assert result =~ "operation=\"TestOp\""
    end

    test "formats operation logs with format tuple message" do
      log_event = %{
        level: :warning,
        msg: {"Step ~s failed with ~p", ["execute", :timeout]},
        meta: %{operation: "TestOp", step: :execute},
        time: System.os_time(:microsecond)
      }

      result = Formatter.format(log_event, :string)

      assert result =~ "[warning] Step execute failed with timeout"
      assert result =~ "operation=\"TestOp\""
      assert result =~ "step=:execute"
    end

    test "returns empty string for non-operation logs" do
      log_event = %{
        level: :debug,
        msg: {:string, "Regular log message"},
        meta: %{},
        time: System.os_time(:microsecond)
      }

      result = Formatter.format(log_event, :string)

      assert result == ""
    end

    test "handles logs with only operation metadata" do
      log_event = %{
        level: :debug,
        msg: {:string, "Operation started"},
        meta: %{operation: "TestOp"},
        time: System.os_time(:microsecond)
      }

      result = Formatter.format(log_event, :string)

      assert result =~ "[debug] Operation started"
      assert result =~ "operation=\"TestOp\""
      refute result =~ "step="
    end

    test "handles logs with only step metadata" do
      log_event = %{
        level: :debug,
        msg: {:string, "Step completed"},
        meta: %{step: :validate},
        time: System.os_time(:microsecond)
      }

      result = Formatter.format(log_event, :string)

      assert result =~ "[debug] Step completed"
      assert result =~ "step=:validate"
      refute result =~ "operation="
    end
  end

  describe "format/2 with JSON formatter" do
    test "formats operation logs as JSON" do
      timestamp = System.os_time(:microsecond)

      log_event = %{
        level: :debug,
        msg: {:string, "Starting step execute"},
        meta: %{operation: "TestOp", step: :execute},
        time: timestamp
      }

      result = Formatter.format(log_event, :json)

      # Parse JSON to verify structure
      assert {:ok, json} = Jason.decode(result)
      assert json["level"] == "debug"
      assert json["message"] == "Starting step execute"
      assert json["metadata"]["operation"] == "TestOp"
      assert json["metadata"]["step"] == "execute"
      assert json["timestamp"] == div(timestamp, 1000)
    end

    test "handles JSON encoding errors gracefully" do
      # Use a function that can't be JSON encoded
      log_event = %{
        level: :debug,
        msg: {:string, "Test message"},
        meta: %{operation: "TestOp", step: fn -> :test end},
        time: System.os_time(:microsecond)
      }

      result = Formatter.format(log_event, :json)

      # Should fallback to inspect when JSON encoding fails
      assert result =~ "level: :debug"
      assert result =~ "message: \"Test message\""
      # Metadata should be inspected as fallback
      assert result =~ "operation: \"TestOp\""
    end

    test "returns empty string for non-operation logs" do
      log_event = %{
        level: :debug,
        msg: {:string, "Regular log message"},
        meta: %{},
        time: System.os_time(:microsecond)
      }

      result = Formatter.format(log_event, :json)

      assert result == ""
    end
  end

  describe "message extraction" do
    test "extracts binary messages" do
      log_event = %{
        level: :debug,
        msg: "Test message",
        meta: %{operation: "TestOp"},
        time: System.os_time(:microsecond)
      }

      assert Formatter.string(log_event, nil) =~ "Test message"
    end

    test "extracts list messages" do
      log_event = %{
        level: :debug,
        msg: [~c"Test", ~c" message"],
        meta: %{operation: "TestOp"},
        time: System.os_time(:microsecond)
      }

      result = Formatter.string(log_event, nil)
      assert result =~ "Test message"
    end

    test "extracts format tuple messages" do
      log_event = %{
        level: :debug,
        msg: {"Hello ~s", ["world"]},
        meta: %{operation: "TestOp"},
        time: System.os_time(:microsecond)
      }

      result = Formatter.string(log_event, nil)
      assert result =~ "Hello world"
    end

    test "extracts string tuple messages" do
      log_event = %{
        level: :debug,
        msg: {:string, "String tuple message"},
        meta: %{operation: "TestOp"},
        time: System.os_time(:microsecond)
      }

      result = Formatter.string(log_event, nil)
      assert result =~ "String tuple message"
    end

    test "handles unknown message formats" do
      log_event = %{
        level: :debug,
        msg: %{unknown: "format"},
        meta: %{operation: "TestOp"},
        time: System.os_time(:microsecond)
      }

      result = Formatter.string(log_event, nil)
      assert result =~ "%{unknown: \"format\"}"
    end
  end

  describe "operation log filtering" do
    test "identifies operation logs with operation metadata" do
      log_event = %{
        level: :debug,
        msg: {:string, "Test"},
        meta: %{operation: "TestOp"},
        time: System.os_time(:microsecond)
      }

      refute Formatter.string(log_event, nil) == ""
      refute Formatter.json(log_event, nil) == ""
    end

    test "identifies operation logs with step metadata" do
      log_event = %{
        level: :debug,
        msg: {:string, "Test"},
        meta: %{step: :execute},
        time: System.os_time(:microsecond)
      }

      refute Formatter.string(log_event, nil) == ""
      refute Formatter.json(log_event, nil) == ""
    end

    test "filters out non-operation logs" do
      log_event = %{
        level: :debug,
        msg: {:string, "Test"},
        meta: %{other: "metadata"},
        time: System.os_time(:microsecond)
      }

      assert Formatter.string(log_event, nil) == ""
      assert Formatter.json(log_event, nil) == ""
    end
  end
end
