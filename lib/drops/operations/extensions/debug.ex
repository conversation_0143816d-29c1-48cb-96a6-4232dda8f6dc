defmodule Drops.Operations.Extensions.Debug do
  @moduledoc """
  Debug extension for Operations framework.

  This extension provides debug logging for Operations by leveraging telemetry events
  and a custom log handler. When enabled, it automatically attaches telemetry handlers
  that log operation and step execution details using a configurable debug handler.

  ## Features

  - Automatic operation-level debug logging (start/stop events)
  - Automatic step-level debug logging (start/stop events for all steps)
  - Configurable log handler (console, file, or memory)
  - Metadata includes operation module, step name, and execution context
  - Built on top of the Telemetry extension

  ## Configuration

  The debug handler can be configured via application environment:

      config :drops, :logger,
        handler: :file,
        file: "log/operations.log",
        level: :debug,
        format: "[$level] $message $metadata\\n",
        metadata: [:operation, :step]

  ## Usage

  ### Enable Debug Logging

  Enable debug logging with default behavior:

      defmodule CreateUser do
        use Drops.Operations.Command, debug: true

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  ### Custom Configuration

  You can also pass configuration options:

      defmodule CreateUser do
        use Drops.Operations.Command, debug: [identifier: :my_app]

        steps do
          @impl true
          def execute(%{params: params}) do
            {:ok, create_user(params)}
          end
        end
      end

  ## Debug Output

  The extension logs the following events at debug level:

  ### Operation-Level Events

  - Operation start: "Starting operation ModuleName with step :step_name"
  - Operation stop: "Completed operation ModuleName in Xms"
  - Operation exception: "Failed in ModuleName: error_reason"

  ### Step-Level Events

  - Step start: "Starting step :step_name in ModuleName"
  - Step stop: "Completed step :step_name in Xms"
  - Step exception: "Failed step :step_name in ModuleName: error_reason"

  ## Implementation Details

  This extension works by:

  1. Enabling telemetry with both operation and step-level instrumentation
  2. Attaching telemetry handlers that log events using the custom debug handler
  3. Automatically cleaning up handlers when the operation module is unloaded

  The extension uses the Telemetry extension internally and attaches handlers
  during module compilation.
  """
  use Drops.Operations.Extension

  require Logger

  @depends_on [Drops.Operations.Extensions.Telemetry]

  @impl true
  @spec enable?(keyword()) :: boolean()
  def enable?(opts) do
    case Keyword.get(opts, :debug, false) do
      false -> false
      true -> true
      config when is_list(config) -> true
    end
  end

  @impl true
  @spec default_opts(keyword()) :: keyword()
  def default_opts(opts) do
    debug_config = Keyword.get(opts, :debug, false)

    case debug_config do
      false ->
        []

      true ->
        # Enable telemetry with all steps instrumented
        [telemetry: [steps: :all]]

      config when is_list(config) ->
        # Pass through custom identifier but ensure all steps are instrumented
        identifier = Keyword.get(config, :identifier, :drops)
        [telemetry: [identifier: identifier, steps: :all]]
    end
  end

  @impl true
  @spec unit_of_work(Drops.Operations.UnitOfWork.t(), keyword()) ::
          Drops.Operations.UnitOfWork.t()
  def unit_of_work(uow, opts) do
    debug_config = Keyword.get(opts, :debug, false)

    case debug_config do
      false ->
        uow

      _ ->
        # Attach debug handlers immediately during compilation
        # This ensures they're available when the operation runs
        attach_debug_handlers(uow.module, debug_config)
        uow
    end
  end

  @impl true
  @spec using() :: Macro.t()
  def using do
    quote do
      # Ensure handlers are cleaned up when module is unloaded
      @before_compile Drops.Operations.Extensions.Debug
    end
  end

  @impl true
  @spec helpers() :: Macro.t()
  def helpers do
    quote do
      # No additional helpers needed
    end
  end

  @impl true
  @spec steps() :: Macro.t()
  def steps do
    quote do
      # No additional steps needed
    end
  end

  defmacro __before_compile__(_env) do
    quote do
      def __debug_handler_id__, do: "debug-#{__MODULE__}"

      # Clean up handlers when module is unloaded
      @on_load :__attach_debug_handlers__
      @before_compile :__detach_debug_handlers__

      def __attach_debug_handlers__ do
        # Handlers are attached in unit_of_work/2, this is just a placeholder
        :ok
      end

      def __detach_debug_handlers__ do
        try do
          :telemetry.detach(__debug_handler_id__())
        rescue
          _ -> :ok
        end
      end
    end
  end

  # Private functions

  defp attach_debug_handlers(operation_module, debug_config) do
    identifier = get_identifier(debug_config)
    handler_id = "debug-#{operation_module}"

    # Define the events we want to listen to
    events = [
      [identifier, :operation, :start],
      [identifier, :operation, :stop],
      [identifier, :operation, :exception],
      [identifier, :operation, :step, :start],
      [identifier, :operation, :step, :stop],
      [identifier, :operation, :step, :exception]
    ]

    # Attach the handler
    :telemetry.attach_many(
      handler_id,
      events,
      &__MODULE__.handle_debug_event/4,
      %{operation_module: operation_module}
    )
  end

  defp get_identifier(debug_config) do
    case debug_config do
      true -> :drops
      config when is_list(config) -> Keyword.get(config, :identifier, :drops)
      _ -> :drops
    end
  end

  @doc false
  def handle_debug_event(
        [_identifier, :operation, :start],
        measurements,
        metadata,
        _config
      ) do
    Logger.debug("Starting operation #{metadata.operation} with step #{metadata.step}",
      operation: metadata.operation,
      step: metadata.step,
      context: metadata.context,
      system_time: measurements.system_time
    )
  end

  def handle_debug_event(
        [_identifier, :operation, :stop],
        measurements,
        metadata,
        _config
      ) do
    duration_ms = System.convert_time_unit(measurements.duration, :native, :millisecond)

    Logger.debug("Completed operation #{metadata.operation} in #{duration_ms}ms",
      operation: metadata.operation,
      step: metadata.step,
      context: metadata.context,
      duration_ms: duration_ms
    )
  end

  def handle_debug_event(
        [_identifier, :operation, :exception],
        measurements,
        metadata,
        _config
      ) do
    duration_ms = System.convert_time_unit(measurements.duration, :native, :millisecond)

    Logger.debug("Failed in #{metadata.operation}: #{inspect(metadata.reason)}",
      operation: metadata.operation,
      step: metadata.step,
      context: metadata.context,
      duration_ms: duration_ms,
      kind: metadata.kind,
      reason: metadata.reason
    )
  end

  def handle_debug_event(
        [_identifier, :operation, :step, :start],
        measurements,
        metadata,
        _config
      ) do
    Logger.debug("Starting step #{metadata.step} in #{metadata.operation}",
      operation: metadata.operation,
      step: metadata.step,
      context: metadata.context,
      system_time: measurements.system_time
    )
  end

  def handle_debug_event(
        [_identifier, :operation, :step, :stop],
        measurements,
        metadata,
        _config
      ) do
    duration_ms = System.convert_time_unit(measurements.duration, :native, :millisecond)

    Logger.debug("Completed step #{metadata.step} in #{duration_ms}ms",
      operation: metadata.operation,
      step: metadata.step,
      context: metadata.context,
      duration_ms: duration_ms
    )
  end

  def handle_debug_event(
        [_identifier, :operation, :step, :exception],
        measurements,
        metadata,
        _config
      ) do
    duration_ms = System.convert_time_unit(measurements.duration, :native, :millisecond)

    Logger.debug(
      "Failed step #{metadata.step} in #{metadata.operation}: #{inspect(metadata.reason)}",
      operation: metadata.operation,
      step: metadata.step,
      context: metadata.context,
      duration_ms: duration_ms,
      kind: metadata.kind,
      reason: metadata.reason
    )
  end
end
