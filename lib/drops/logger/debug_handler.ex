defmodule Drops.Logger.DebugHandler do
  @moduledoc """
  Memory log handler for Drops operations debug logging in tests.

  This module provides a memory-based log handler for testing purposes only.
  For console and file logging, Drops uses built-in Erlang logger handlers
  with custom formatters for better performance and consistency.

  ## Memory Handler

  When using `:memory` handler, logs can be retrieved and cleared:

      # Get all captured logs
      logs = Drops.Logger.DebugHandler.get_logs()

      # Clear captured logs
      Drops.Logger.DebugHandler.clear_logs()

  This is particularly useful for testing where you want to assert on log content.
  """

  @doc """
  Gets all captured logs when using memory handler.

  Returns an empty list if not using memory handler or no logs captured.
  """
  @spec get_logs() :: [String.t()]
  def get_logs do
    case :persistent_term.get({__MODULE__, :logs}, nil) do
      nil -> []
      logs -> Enum.reverse(logs)
    end
  end

  @doc """
  Clears all captured logs when using memory handler.
  """
  @spec clear_logs() :: :ok
  def clear_logs do
    :persistent_term.put({__MODULE__, :logs}, [])
    :ok
  end

  # Logger handler callbacks for memory handler only

  def adding_handler(config) do
    # Initialize memory storage for memory handler
    :persistent_term.put({__MODULE__, :logs}, [])
    {:ok, config}
  end

  def removing_handler(_config) do
    # Clean up memory storage
    try do
      :persistent_term.erase({__MODULE__, :logs})
    rescue
      _ -> :ok
    end

    :ok
  end

  def log(log_event, _config) do
    # Only process debug level logs that are operation-related
    if log_event.level == :debug and is_operation_log?(log_event) do
      # For memory handler, just store the formatted message
      formatted_message = format_message(log_event)
      current_logs = :persistent_term.get({__MODULE__, :logs}, [])
      :persistent_term.put({__MODULE__, :logs}, [formatted_message | current_logs])
    end

    :ok
  end

  def changing_config(_set_or_update, _old_config, new_config) do
    # Re-validate configuration on changes
    adding_handler(new_config)
  end

  def filter_config(config) do
    # Remove internal state from config when fetched
    config
  end

  # Private functions

  defp is_operation_log?(log_event) do
    meta = Map.get(log_event, :meta, %{})
    # Check if the log has operation-related metadata
    Map.has_key?(meta, :operation) or Map.has_key?(meta, :step)
  end

  defp format_message(log_event) do
    level = Map.get(log_event, :level, :debug)
    message = extract_message(log_event)
    metadata = extract_metadata(log_event, [:operation, :step])

    # Use a simple format without newlines
    "[#{level}] #{message}#{metadata}"
  end

  defp extract_message(%{msg: msg}) when is_binary(msg), do: msg
  defp extract_message(%{msg: msg}) when is_list(msg), do: IO.iodata_to_binary(msg)

  defp extract_message(%{msg: {format, args}}) when is_list(args),
    do: :io_lib.format(format, args) |> IO.iodata_to_binary()

  defp extract_message(%{msg: msg}), do: inspect(msg)
  defp extract_message(_), do: ""

  defp extract_metadata(log_event, metadata_keys) do
    meta = Map.get(log_event, :meta, %{})

    metadata_keys
    |> Enum.map(fn key ->
      case Map.get(meta, key) do
        nil -> nil
        value -> "#{key}=#{inspect(value)}"
      end
    end)
    |> Enum.filter(& &1)
    |> case do
      [] -> ""
      items -> " " <> Enum.join(items, " ")
    end
  end
end
