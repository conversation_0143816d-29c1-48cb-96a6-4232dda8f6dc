defmodule Drops.Logger.DebugHandler do
  @moduledoc """
  Custom log handler for Drops operations debug logging.

  This handler provides configurable logging for operations debug information,
  supporting file logging, in-memory capture for testing, and console output.

  ## Configuration

  The handler can be configured via application environment:

      config :drops, :logger,
        handler: :file,
        file: "log/operations.log",
        level: :debug,
        format: "[$level] $message $metadata\n",
        metadata: [:operation, :step]

  ## Handler Types

  * `:console` - Logs to standard output (default)
  * `:file` - Logs to a specified file with automatic directory creation
  * `:memory` - Captures logs in memory for testing purposes

  ## Memory Handler

  When using `:memory` handler, logs can be retrieved and cleared:

      # Get all captured logs
      logs = Drops.Logger.DebugHandler.get_logs()

      # Clear captured logs
      Drops.Logger.DebugHandler.clear_logs()

  This is particularly useful for testing where you want to assert on log content.
  """

  require Logger

  @default_config %{
    handler: :console,
    file: "log/drops_debug.log",
    level: :debug,
    format: "[$level] $message $metadata\n",
    metadata: [:operation, :step]
  }

  @doc """
  Gets all captured logs when using memory handler.

  Returns an empty list if not using memory handler or no logs captured.
  """
  @spec get_logs() :: [String.t()]
  def get_logs do
    case :persistent_term.get({__MODULE__, :logs}, nil) do
      nil -> []
      logs -> Enum.reverse(logs)
    end
  end

  @doc """
  Clears all captured logs when using memory handler.
  """
  @spec clear_logs() :: :ok
  def clear_logs do
    :persistent_term.put({__MODULE__, :logs}, [])
    :ok
  end

  # Logger handler callbacks

  def adding_handler(config) do
    # Validate and initialize the handler configuration
    handler_config = Map.get(config, :config, %{})
    merged_config = Map.merge(@default_config, handler_config)

    # Ensure log directory exists for file handler
    if merged_config.handler == :file do
      log_dir = Path.dirname(merged_config.file)
      File.mkdir_p!(log_dir)
    end

    # Initialize memory storage for memory handler
    if merged_config.handler == :memory do
      :persistent_term.put({__MODULE__, :logs}, [])
    end

    {:ok, Map.put(config, :config, merged_config)}
  end

  def removing_handler(_config) do
    # Clean up memory storage if using memory handler
    try do
      :persistent_term.erase({__MODULE__, :logs})
    rescue
      _ -> :ok
    end

    :ok
  end

  def log(log_event, config) do
    handler_config = Map.get(config, :config, @default_config)

    # Only process debug level logs that are operation-related
    if log_event.level == :debug and is_operation_log?(log_event) do
      formatted_message = format_message(log_event, handler_config)

      case handler_config.handler do
        :console ->
          IO.puts(formatted_message)

        :file ->
          File.write!(handler_config.file, formatted_message, [:append])

        :memory ->
          current_logs = :persistent_term.get({__MODULE__, :logs}, [])
          :persistent_term.put({__MODULE__, :logs}, [formatted_message | current_logs])
      end
    end

    :ok
  end

  def changing_config(_set_or_update, _old_config, new_config) do
    # Re-validate configuration on changes
    adding_handler(new_config)
  end

  def filter_config(config) do
    # Remove internal state from config when fetched
    config
  end

  # Formatter callback (used when handler acts as formatter)
  def format(log_event, config) do
    format_message(log_event, config)
  end

  # Private functions

  defp is_operation_log?(log_event) do
    meta = Map.get(log_event, :meta, %{})
    # Check if the log has operation-related metadata
    Map.has_key?(meta, :operation) or Map.has_key?(meta, :step)
  end

  defp format_message(log_event, config) do
    level = Map.get(log_event, :level, :debug)
    message = extract_message(log_event)
    metadata = extract_metadata(log_event, config.metadata)

    config.format
    |> String.replace("$level", to_string(level))
    |> String.replace("$message", message)
    |> String.replace("$metadata", metadata)
  end

  defp extract_message(%{msg: msg}) when is_binary(msg), do: msg
  defp extract_message(%{msg: msg}) when is_list(msg), do: IO.iodata_to_binary(msg)

  defp extract_message(%{msg: {format, args}}) when is_list(args),
    do: :io_lib.format(format, args) |> IO.iodata_to_binary()

  defp extract_message(%{msg: msg}), do: inspect(msg)
  defp extract_message(_), do: ""

  defp extract_metadata(log_event, metadata_keys) do
    meta = Map.get(log_event, :meta, %{})

    metadata_keys
    |> Enum.map(fn key ->
      case Map.get(meta, key) do
        nil -> nil
        value -> "#{key}=#{inspect(value)}"
      end
    end)
    |> Enum.filter(& &1)
    |> case do
      [] -> ""
      items -> " " <> Enum.join(items, " ")
    end
  end
end
