defmodule Drops.Logger.DebugHandler do
  @moduledoc """
  Custom log handler for Drops operations debug logging.

  This handler provides configurable logging for operations debug information,
  using built-in Erlang logger handlers and formatters for better performance
  and consistency.

  ## Configuration

  The handler can be configured via application environment:

      config :drops, :logger,
        handler: :console,
        file: "log/operations.log",
        level: :debug,
        formatter: :string,
        metadata: [:operation, :step]

  ## Handler Types

  * `:console` - Logs to standard output using built-in console handler (default)
  * `:file` - Logs to a specified file using built-in file handler
  * `:memory` - Captures logs in memory for testing purposes

  ## Formatter Types

  * `:string` - Human-readable string format with message and metadata (default)
  * `:json` - JSON format with message and metadata as structured data

  ## Memory Handler

  When using `:memory` handler, logs can be retrieved and cleared:

      # Get all captured logs
      logs = Drops.Logger.DebugHandler.get_logs()

      # Clear captured logs
      Drops.Logger.DebugHandler.clear_logs()

  This is particularly useful for testing where you want to assert on log content.
  """

  require Logger

  @default_config %{
    handler: :console,
    file: "log/drops_debug.log",
    level: :debug,
    formatter: :string,
    metadata: [:operation, :step]
  }

  @doc """
  Gets all captured logs when using memory handler.

  Returns an empty list if not using memory handler or no logs captured.
  """
  @spec get_logs() :: [String.t()]
  def get_logs do
    case :persistent_term.get({__MODULE__, :logs}, nil) do
      nil -> []
      logs -> Enum.reverse(logs)
    end
  end

  @doc """
  Clears all captured logs when using memory handler.
  """
  @spec clear_logs() :: :ok
  def clear_logs do
    :persistent_term.put({__MODULE__, :logs}, [])
    :ok
  end

  # Logger handler callbacks

  def adding_handler(config) do
    # Validate and initialize the handler configuration
    handler_config = Map.get(config, :config, %{})
    merged_config = Map.merge(@default_config, handler_config)

    # Ensure log directory exists for file handler
    if merged_config.handler == :file do
      log_dir = Path.dirname(merged_config.file)
      File.mkdir_p!(log_dir)
    end

    # Initialize memory storage for memory handler
    if merged_config.handler == :memory do
      :persistent_term.put({__MODULE__, :logs}, [])
    end

    {:ok, Map.put(config, :config, merged_config)}
  end

  def removing_handler(_config) do
    # Clean up memory storage if using memory handler
    try do
      :persistent_term.erase({__MODULE__, :logs})
    rescue
      _ -> :ok
    end

    :ok
  end

  def log(log_event, config) do
    handler_config = Map.get(config, :config, @default_config)

    # Only process debug level logs that are operation-related
    if log_event.level == :debug and is_operation_log?(log_event) do
      case handler_config.handler do
        :console ->
          # Use built-in console handler via Logger
          formatted_message = format_message(log_event, handler_config)
          # Remove trailing newline since Logger.bare_log adds its own
          message = String.trim_trailing(formatted_message, "\n")
          Logger.bare_log(:debug, message, log_event.meta)

        :file ->
          # Use built-in file logging via Logger
          formatted_message = format_message(log_event, handler_config)
          # For file logging, we'll still use direct file writing for now
          # but format without extra newlines
          message = String.trim_trailing(formatted_message, "\n") <> "\n"
          File.write!(handler_config.file, message, [:append])

        :memory ->
          formatted_message = format_message(log_event, handler_config)
          # Store without extra newlines
          message = String.trim_trailing(formatted_message, "\n")
          current_logs = :persistent_term.get({__MODULE__, :logs}, [])
          :persistent_term.put({__MODULE__, :logs}, [message | current_logs])
      end
    end

    :ok
  end

  def changing_config(_set_or_update, _old_config, new_config) do
    # Re-validate configuration on changes
    adding_handler(new_config)
  end

  def filter_config(config) do
    # Remove internal state from config when fetched
    config
  end

  # Formatter callback (used when handler acts as formatter)
  def format(log_event, config) do
    format_message(log_event, config)
  end

  # Private functions

  defp is_operation_log?(log_event) do
    meta = Map.get(log_event, :meta, %{})
    # Check if the log has operation-related metadata
    Map.has_key?(meta, :operation) or Map.has_key?(meta, :step)
  end

  defp format_message(log_event, config) do
    case Map.get(config, :formatter, :string) do
      :string ->
        format_string_message(log_event, config)

      :json ->
        format_json_message(log_event, config)

      _ ->
        # Fallback to string format for unknown formatters
        format_string_message(log_event, config)
    end
  end

  defp format_string_message(log_event, config) do
    level = Map.get(log_event, :level, :debug)
    message = extract_message(log_event)
    metadata = extract_metadata(log_event, config.metadata)

    # Use a simple format without newlines (they'll be added by the handler)
    "[#{level}] #{message} #{metadata}"
  end

  defp format_json_message(log_event, config) do
    level = Map.get(log_event, :level, :debug)
    message = extract_message(log_event)
    metadata = extract_metadata_map(log_event, config.metadata)

    json_data = %{
      level: level,
      message: message,
      metadata: metadata,
      timestamp: System.system_time(:millisecond)
    }

    case Code.ensure_loaded(Jason) do
      {:module, Jason} ->
        Jason.encode!(json_data)

      {:error, _} ->
        # Fallback to inspect if Jason is not available
        inspect(json_data)
    end
  end

  defp extract_message(%{msg: msg}) when is_binary(msg), do: msg
  defp extract_message(%{msg: msg}) when is_list(msg), do: IO.iodata_to_binary(msg)

  defp extract_message(%{msg: {format, args}}) when is_list(args),
    do: :io_lib.format(format, args) |> IO.iodata_to_binary()

  defp extract_message(%{msg: msg}), do: inspect(msg)
  defp extract_message(_), do: ""

  defp extract_metadata(log_event, metadata_keys) do
    meta = Map.get(log_event, :meta, %{})

    metadata_keys
    |> Enum.map(fn key ->
      case Map.get(meta, key) do
        nil -> nil
        value -> "#{key}=#{inspect(value)}"
      end
    end)
    |> Enum.filter(& &1)
    |> case do
      [] -> ""
      items -> " " <> Enum.join(items, " ")
    end
  end

  defp extract_metadata_map(log_event, metadata_keys) do
    meta = Map.get(log_event, :meta, %{})

    metadata_keys
    |> Enum.reduce(%{}, fn key, acc ->
      case Map.get(meta, key) do
        nil -> acc
        value -> Map.put(acc, key, value)
      end
    end)
  end
end
