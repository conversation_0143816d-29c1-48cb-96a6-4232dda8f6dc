defmodule Drops.Logger.Formatter do
  @moduledoc """
  Custom formatters for Drops operations debug logging.

  This module provides formatters that work with built-in Erlang logger handlers
  to format operation debug logs in different formats.

  ## Formatter Types

  * `:string` - Human-readable string format with message and metadata (default)
  * `:json` - JSON format with message and metadata as structured data

  ## Usage

  These formatters are used automatically by the Drops logger system when
  configuring built-in handlers for console and file logging.
  """

  @doc """
  Main formatter function called by built-in logger handlers.

  This function dispatches to the appropriate formatter based on the config.
  """
  def format(log_event, config) when is_atom(config) do
    # When config is an atom, it's the formatter type
    format_with_type(log_event, config)
  end

  def format(log_event, config) when is_map(config) do
    # When config is a map, extract the formatter type
    formatter_type = Map.get(config, :formatter, :string)
    format_with_type(log_event, formatter_type)
  end

  defp format_with_type(log_event, :string) do
    string(log_event, %{})
  end

  defp format_with_type(log_event, :json) do
    json(log_event, %{})
  end

  defp format_with_type(log_event, _) do
    # Default to string format
    string(log_event, %{})
  end

  @doc """
  String formatter for operation debug logs.

  Formats logs in a human-readable string format with operation metadata.
  Only processes logs that have operation-related metadata.

  ## Format

  The output format is: `[level] message metadata`

  Where metadata includes operation and step information when available.
  """
  def string(log_event, _config) do
    if is_operation_log?(log_event) do
      level = Map.get(log_event, :level, :debug)
      message = extract_message(log_event)
      metadata = extract_metadata(log_event, [:operation, :step])

      # Format without trailing newline - the handler will add it
      "[#{level}] #{message}#{metadata}"
    else
      # Return empty string for non-operation logs to filter them out
      ""
    end
  end

  @doc """
  JSON formatter for operation debug logs.

  Formats logs as JSON with structured metadata.
  Only processes logs that have operation-related metadata.

  ## Format

  The output is a JSON object with the following structure:

      {
        "level": "debug",
        "message": "Operation message",
        "metadata": {
          "operation": "OperationName",
          "step": "step_name"
        },
        "timestamp": 1234567890123
      }

  If Jason is not available, falls back to using `inspect/1`.
  """
  def json(log_event, _config) do
    if is_operation_log?(log_event) do
      level = Map.get(log_event, :level, :debug)
      message = extract_message(log_event)
      metadata = extract_metadata_map(log_event, [:operation, :step])

      json_data = %{
        level: level,
        message: message,
        metadata: metadata,
        timestamp: System.system_time(:millisecond)
      }

      case Code.ensure_loaded(Jason) do
        {:module, Jason} ->
          Jason.encode!(json_data)

        {:error, _} ->
          # Fallback to inspect if Jason is not available
          inspect(json_data)
      end
    else
      # Return empty string for non-operation logs to filter them out
      ""
    end
  end

  # Private functions

  defp is_operation_log?(log_event) do
    meta = Map.get(log_event, :meta, %{})
    # Check if the log has operation-related metadata
    Map.has_key?(meta, :operation) or Map.has_key?(meta, :step)
  end

  defp extract_message(%{msg: msg}) when is_binary(msg), do: msg
  defp extract_message(%{msg: msg}) when is_list(msg), do: IO.iodata_to_binary(msg)

  defp extract_message(%{msg: {format, args}}) when is_list(args),
    do: :io_lib.format(format, args) |> IO.iodata_to_binary()

  # Handle {:string, message} tuples from Logger calls
  defp extract_message(%{msg: {:string, msg}}) when is_binary(msg), do: msg

  defp extract_message(%{msg: msg}), do: inspect(msg)
  defp extract_message(_), do: ""

  defp extract_metadata(log_event, metadata_keys) do
    meta = Map.get(log_event, :meta, %{})

    metadata_keys
    |> Enum.map(fn key ->
      case Map.get(meta, key) do
        nil -> nil
        value -> "#{key}=#{inspect(value)}"
      end
    end)
    |> Enum.filter(& &1)
    |> case do
      [] -> ""
      items -> " " <> Enum.join(items, " ")
    end
  end

  defp extract_metadata_map(log_event, metadata_keys) do
    meta = Map.get(log_event, :meta, %{})

    metadata_keys
    |> Enum.reduce(%{}, fn key, acc ->
      case Map.get(meta, key) do
        nil -> acc
        value -> Map.put(acc, key, value)
      end
    end)
  end
end
